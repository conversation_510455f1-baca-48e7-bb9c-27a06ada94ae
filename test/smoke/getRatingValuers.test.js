import * as service from './endpoints.js';
import { validateUserArray } from '../common/validators.js';import { expect } from "chai";
import { getQivsPool } from '../../src/dal/sqlConnectionPool.js';
import mssql from 'mssql';
import { parseStringPromise } from 'xml2js';
import { User } from '../../src/models/user.js';
import { Office } from '../../src/models/office.js';

let result = null;
let storedProcUsers = null;

describe('get rating valuers', () => {
  before(async () => {
    const testOptions = {
      isValuer: true,
      isActive: true,
      pageSize: 5000,
      offset: 0,
      orderBy: 'name',
      desc: false,
    };

    storedProcUsers = await getStoredProcResults(testOptions);
    result = await service.getRatingValuers();
  });

  it('responds 200 OK', () => {
    expect(result).to.have.status(200);
  });

  it('returns valid users array', () => {
    validateUserArray(result.body);
  });

  it('returns only valuers and senior valuers', () => {
    result.body.forEach((user) => {
      expect(user.roles).to.include.oneOf(['Valuer', 'Senior Valuer']);
    });
  });

  it('returns same number of users as stored procedure', () => {
    const jsResults = result.body;
    expect(jsResults.length).to.greaterThan(0);
    expect(jsResults.length).to.equal(storedProcUsers.length);
  });

  it('returns identical user data to stored procedure', () => {
    const jsResults = result.body;
    for (let i = 0; i < storedProcUsers.length; i++) {
      const spUser = storedProcUsers[i];
      const jsUser = jsResults[i];
      expect(jsUser).to.deep.equal(spUser, `User should be identical at index ${i}`);
    }
  });

  async function getStoredProcResults(testOptions) {
    const pool = await getQivsPool();
    const criteriaXml = `
    <criteria>
      <username></username>
      <is_valuer_yn>true</is_valuer_yn>
      <is_registered_valuer_yn></is_registered_valuer_yn>
      <is_active_yn>true</is_active_yn>
    </criteria>
  `;

    const storedProcResult = await pool
      .request()
      .input('criteriaXml', mssql.NVarChar, criteriaXml)
      .input('pageSize', mssql.Int, testOptions.pageSize)
      .input('offset', mssql.Int, testOptions.offset)
      .input('orderBy', mssql.VarChar, testOptions.orderBy)
      .input('desc', mssql.Bit, testOptions.desc)
      .execute('spd_MONARCH_searchUsers');

    const users = [];
    for (const row of storedProcResult.recordset) {
      const userXml = row.user_xml;
      const user = await fromXml(userXml);
      users.push(user);
    }
    return users;
  }

  async function fromXml(xml) {
    const parsed = await parseStringPromise(xml, {
      explicitArray: false,
      mergeAttrs: true,
    });

    const user = new User();
    user.id = null;
    user.externalId = parsed.user?.username || null;
    user.name = parsed.user?.name || null;
    user.ntUsername = parsed.user?.nt_user_name || null;
    user.qualifications = parsed.user?.qualifications || null;
    user.email = parsed.user?.email_address || null;
    user.admin = null;
    user.target = null;
    user.prevTarget = null;
    user.roles = [];

    if (parsed.user.is_registered_valuer_yn === 'true') {
      user.roles.push('Senior Valuer');
    } else if (parsed.user.is_valuer_yn === 'true') {
      user.roles.push('Valuer');
    }

    if (parsed.user.office) {
      const office = new Office();
      const officeData = parsed.user.office;

      office.id = null;
      office.name = officeData.name;
      office.phoneNumber = officeData.phone_number;
      office.faxNumber = officeData.fax_number;
      office.addressLine1 = officeData.address_line_1;
      office.addressLine2 = officeData.address_line_2;
      office.addressLine3 = officeData.address_line_3;
      office.addressLine4 = officeData.address_line_4;
      office.addressLine5 = officeData.address_line_5;
      office.postCode = officeData.post_code;

      user.office = office;
    } else {
      user.office = new Office();
    }

    return user;
  }

});
