import { Office } from './office.js';

export class User {
  /** @type {string} */
  id;
  /** @type {string} */
  externalId;
  /** @type {string} */
  name;
  /** @type {string} */
  ntUsername;
  /** @type {string} */
  qualifications;
  /** @type {string} */
  email;
  /** @type {string} */
  target;
  /** @type {string} */
  prevTarget;
  /** @type {string[]} */
  roles;
  /** @type {boolean} */
  valuer;
  /** @type {boolean} */
  admin;
  /** @type {boolean} */
  registeredValuer;
  /** @type {boolean} */
  canValueRatingUnit;
  /** @type {Office} */
  office;

  constructor(data = {}) {
    Object.assign(this, data);

    this.roles = this.roles || [];
    this.valuer = this.valuer || false;
    this.admin = this.admin || false;
    this.registeredValuer = this.registeredValuer || false;
    this.canValueRatingUnit = this.canValueRatingUnit || false;

    if (data.office) {
      this.office = data.office instanceof Office ? data.office : new Office(data.office);
    } else {
      this.office = new Office();
    }
  }


  static fromDatabaseRecordset(recordset) {
    const row = recordset[0];
    const user = new User();

    user.id = row.id;
    user.externalId = null;
    user.name = row.name;
    user.ntUsername = row.nt_username;
    user.qualifications = row.qualifications;
    user.email = row.email;
    user.target = row.target_role;
    user.prevTarget = row.h_target_role;
    user.roles = [];
    user.valuer = false;
    user.admin = false;
    user.registeredValuer = false;
    user.canValueRatingUnit = false;

    user.office = Office.fromDatabaseRow({
      id: row.office_id,
      name: row.office_name,
      phone_number: row.phone_number,
      fax_number: row.fax_number,
      address_line_1: row.address_line_1,
      address_line_2: row.address_line_2,
      address_line_3: row.address_line_3,
      address_line_4: row.address_line_4,
      address_line_5: row.address_line_5,
      post_code: row.post_code
    });

    for (const row of recordset) {
      if (row.role && !user.roles.includes(row.role)) {
        user.roles.push(row.role);
      }
    }

    return user;
  }

  static fromDatabaseRecordsets(recordset) {
    const users = [];
    let currentUserId = null;

    for (const row of recordset) {
      if (row.id === currentUserId) {
        if (row.role && !users[users.length - 1].roles.includes(row.role)) {
          users[users.length - 1].roles.push(row.role);
        }
      } else {
        users.push(User.fromDatabaseRecordset([row]));
        currentUserId = row.id;
      }
    }

    return users;
  }

  static fromSearchResultRow(row) {
    const user = new User();

    user.id = null;
    user.externalId = row.username || null;
    user.name = row.name || null;
    user.ntUsername = row.nt_user_name || null;
    user.qualifications = row.qualifications || null;
    user.email = row.email_address || null;
    user.admin = null;
    user.target = null;
    user.prevTarget = null;
    user.roles = [];

    if (row.is_registered_valuer_yn === 'true') {
      user.roles.push('Senior Valuer');
    } else if (row.is_valuer_yn === 'true') {
      user.roles.push('Valuer');
    }

    user.office = new Office({
      id: null,
      name: row.office_name,
      phoneNumber: row.office_phone_number,
      faxNumber: row.office_fax_number,
      addressLine1: row.office_address_line_1,
      addressLine2: row.office_address_line_2,
      addressLine3: row.office_address_line_3,
      addressLine4: row.office_address_line_4,
      addressLine5: row.office_address_line_5,
      postCode: row.office_post_code
    });

    return user;
  }
}