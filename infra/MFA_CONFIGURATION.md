# MFA Granular Control Configuration

This document describes the granular MFA control implementation for Auth0 using Terraform.

## Overview

The MFA configuration provides fine-grained control over which applications and users require Multi-Factor Authentication (MFA). This follows SRE best practices for security governance and compliance.

## Configuration Variables

### `mfa_policy`
- **Type**: `string`
- **Default**: `"confidence-score"`
- **Options**: `"all-applications"`, `"confidence-score"`, `"never"`
- **Description**: Base MFA policy for Guardian

### `mfa_client_ids`
- **Type**: `list(string)`
- **Default**: `[]` (empty = all applications)
- **Description**: List of client IDs that require MFA
- **Example**: `["LxmTROS8b5mu3Myxyc7SX18EpoNZpxPd", "another_client_id"]`

### `mfa_user_emails`
- **Type**: `list(string)`
- **Default**: `[]` (empty = all users)
- **Description**: List of user emails that require MFA
- **Example**: `["<EMAIL>", "<EMAIL>"]`

## Usage Examples

### Example 1: MFA for All Users and Applications
```hcl
mfa_policy = "confidence-score"
mfa_client_ids = []
mfa_user_emails = []
```

### Example 2: MFA for Specific Applications Only
```hcl
mfa_policy = "confidence-score"
mfa_client_ids = ["LxmTROS8b5mu3Myxyc7SX18EpoNZpxPd"]
mfa_user_emails = []
```

### Example 3: MFA for Specific Users Only
```hcl
mfa_policy = "confidence-score"
mfa_client_ids = []
mfa_user_emails = ["<EMAIL>", "<EMAIL>"]
```

### Example 4: MFA for Specific Applications AND Specific Users
```hcl
mfa_policy = "confidence-score"
mfa_client_ids = ["LxmTROS8b5mu3Myxyc7SX18EpoNZpxPd"]
mfa_user_emails = ["<EMAIL>"]
```

## Logic Flow

The Auth0 Action implements the following logic:

1. **Skip MFA for machine-to-machine flows** (client_credentials grant)
2. **Skip if user has no email**
3. **Check client requirement**: 
   - If `mfa_client_ids` is empty → all clients require MFA
   - If `mfa_client_ids` has values → only listed clients require MFA
4. **Check user requirement**:
   - If `mfa_user_emails` is empty → all users require MFA
   - If `mfa_user_emails` has values → only listed users require MFA
5. **Require MFA if BOTH conditions are met**

## Security Features

- **Audit Trail**: Action logs MFA requirements and login metadata
- **Session Awareness**: Checks if MFA already completed in current session
- **Remember Browser**: Disabled for enhanced security
- **Metadata Tracking**: Records MFA policy decisions in user metadata

## Monitoring and Observability

The Action adds the following metadata for monitoring:
- `mfa_required_by`: "granular_policy"
- `mfa_required_at`: ISO timestamp
- `last_login_client`: Client ID of last login
- `last_login_at`: ISO timestamp of last login

## Deployment

1. Update `terraform.tfvars` with desired configuration
2. Run `terraform plan` to review changes
3. Run `terraform apply` to deploy

## Troubleshooting

- Check Auth0 Action logs in the Auth0 Dashboard
- Verify client IDs are correct
- Ensure user emails match exactly (case-insensitive)
- Review Guardian policy settings
