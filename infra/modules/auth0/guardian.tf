resource "auth0_guardian" "default" {
  policy        = var.mfa_policy
  email         = false
  otp           = false
  recovery_code = true

  webauthn_platform {
    enabled                = var.enable_webauthn_platform
    override_relying_party = false
  }

  webauthn_roaming {
    enabled                = var.enable_webauthn_roaming
    override_relying_party = false
    user_verification      = "preferred"
  }
}
