resource "auth0_connection" "qivdb_connection" {
  name     = "${var.env}-QIVDB"
  strategy = "auth0"

  options {
    disable_signup                 = true
    requires_username              = false
    brute_force_protection         = true
    enabled_database_customization = true
    import_mode                    = true

    password_complexity_options {
      min_length = 1
    }

    password_history {
      size   = 5
      enable = false
    }

    password_dictionary {
      enable     = false
      dictionary = []
    }

    password_no_personal_info {
      enable = false
    }

    mfa {
      active                 = true
      return_enroll_settings = true
    }

    # Enable authentication methods including passkeys
    # Now compatible with New Universal Login Experience
    authentication_methods {
      passkey {
        enabled = var.enable_webauthn_platform || var.enable_webauthn_roaming
      }
      password {
        enabled = true
      }
    }

    configuration = {
      DB_PASSWORD      = var.db_password
      DB_USER_NAME     = var.db_user_name
      DB_SERVER_HOST   = var.db_server_host
      DB_SERVER_PORT   = var.db_server_port
      EXTERNAL_QIVS_URL = var.external_qivs_url
    }

    # Custom scripts from the existing QIVSDB connection
    custom_scripts = {
      login          = file("${path.module}/scripts/login.js")
      create         = file("${path.module}/scripts/create.js")
      delete         = file("${path.module}/scripts/delete.js")
      get_user       = file("${path.module}/scripts/get_user.js")
      verify         = file("${path.module}/scripts/verify.js")
      change_password = file("${path.module}/scripts/change_password.js")
    }
  }

  # Set the realm for this connection
  realms = ["${var.env}-QIVDB"]
}

resource "auth0_connection_client" "monarch_web_qivdb" {
  connection_id = auth0_connection.qivdb_connection.id
  client_id     = auth0_client.monarch_web.id
}
