output "monarch_web_client_id" {
  description = "The client ID of the Monarch Web client"
  value       = auth0_client.monarch_web.client_id
}

output "qivdb_connection_id" {
  description = "The ID of the QIVDB connection"
  value       = auth0_connection.qivdb_connection.id
}

output "qivdb_connection_name" {
  description = "The name of the QIVDB connection"
  value       = auth0_connection.qivdb_connection.name
}

output "guardian_policy" {
  description = "The Guardian MFA policy"
  value       = auth0_guardian.default.policy
}

output "webauthn_enabled" {
  description = "WebAuthn/Passkey authentication status"
  value = {
    platform = auth0_guardian.default.webauthn_platform[0].enabled
    roaming  = auth0_guardian.default.webauthn_roaming[0].enabled
  }
}

output "tenant_flags" {
  description = "Tenant flags for passkey support"
  value = {
    enable_pipeline2 = auth0_tenant.main.flags[0].enable_pipeline2
    enable_sso = auth0_tenant.main.flags[0].enable_sso
  }
}

output "connection_settings" {
  description = "Database connection settings for passkey support"
  value = {
    import_mode = auth0_connection.qivdb_connection.options[0].import_mode
    requires_username = auth0_connection.qivdb_connection.options[0].requires_username
  }
}

output "universal_login_experience" {
  description = "Universal Login Experience configuration"
  value = {
    identifier_first = auth0_prompt.identifier_first.identifier_first
    webauthn_platform_first_factor = auth0_prompt.identifier_first.webauthn_platform_first_factor
  }
}

output "mfa_configuration" {
  description = "MFA granular control configuration"
  value = {
    policy = auth0_guardian.default.policy
    client_ids = var.mfa_client_ids
    user_emails = var.mfa_user_emails
    action_id = auth0_action.mfa_granular_control.id
  }
}
