env                 = "{ENV_NAME}"
monarch_url         = "https://{ENV_NAME}.monarch.internal.quotablevalue.co.nz"
auth0_domain        = "qvmonarch-dev.au.auth0.com"
auth0_client_id     = "2RlhQMtk1BjPozKaLkeBE2eRlyJOuXqF"
auth0_client_secret = "****************************************************************"

# Database connection configuration
db_password      = "{DB_PASSWORD}"
db_user_name     = "{DB_USERNAME}"
db_server_host   = "{DB_HOST}"
db_server_port   = "{DB_PORT}"
external_qivs_url = "{EXTERNAL_QIVS_URL}"

# WebAuthn/Passkey configuration
enable_webauthn_platform = true
enable_webauthn_roaming  = true

# MFA Granular Control Configuration
# MFA policy: "all-applications", "confidence-score", or "never"
mfa_policy = "confidence-score"

# Specific client IDs that require MFA (empty = all applications)
# Example: mfa_client_ids = ["LxmTROS8b5mu3Myxyc7SX18EpoNZpxPd", "another_client_id"]
mfa_client_ids = []

# Specific user emails that require MFA (empty = all users)
# Example: mfa_user_emails = ["<EMAIL>", "<EMAIL>"]
mfa_user_emails = []
